rules:
  - id: incorrect-parameter-order
    message: Potential incorrect parameter order in function call to $FUNC
    metadata:
      category: security
      technology:
        - solidity
      cwe: "CWE-628: Function Call with Incorrectly Specified Arguments"
      confidence: MEDIUM
      likelihood: MEDIUM
      impact: MEDIUM
      subcategory:
        - audit
      references:
        - https://swcregistry.io/docs/SWC-107
    patterns:
      - pattern-either:
        - pattern: |
            contract $C {
              ...
              function $FUNC(address $PARAM1, address $PARAM2, ...) $X {
                ...
              }
              ...
              function $CALLER(...) $Y {
                ...
                $FUNC($PARAM2, $PARAM1, ...);
                ...
              }
              ...
            }
        - pattern: |
            contract $C {
              ...
              function $CALLER(...) $Y {
                ...
                $FUNC($PARAM2, $PARAM1, ...);
                ...
              }
              ...
              function $FUNC(address $PARAM1, address $PARAM2, ...) $X {
                ...
              }
              ...
            }
        - pattern: |
            contract $C {
              ...
              function $FUNC(uint256 $PARAM1, uint256 $PARAM2, ...) $X {
                ...
              }
              ...
              function $CALLER(...) $Y{
                ...
                $FUNC($PARAM2, $PARAM1, ...);
                ...
              }
              ...
            }
        - pattern: |
            contract $C {
              ...
              function $CALLER(...) $Y{
                ...
                $FUNC($PARAM2, $PARAM1, ...);
                ...
              }
              ...
              function $FUNC(uint256 $PARAM1, uint256 $PARAM2, ...) $X {
                ...
              }
              ...
            }
        - pattern: |
            contract $C {
              ...
              function $FUNC(address $PARAM1, address $PARAM2) $X {
                ...
              }
              ...
              function $CALLER(...) $Y {
                ...
                $FUNC($PARAM2, $PARAM1);
                ...
              }
              ...
            }
        - pattern: |
            contract $C {
              ...
              function $CALLER(...) $Y {
                ...
                $FUNC($PARAM2, $PARAM1);
                ...
              }
              ...
              function $FUNC(address $PARAM1, address $PARAM2) $X {
                ...
              }
              ...
            }
        - pattern: |
            contract $C {
              ...
              function $FUNC(uint256 $PARAM1, uint256 $PARAM2) $X {
                ...
              }
              ...
              function $CALLER(...) $Y {
                ...
                $FUNC($PARAM2, $PARAM1);
                ...
              }
              ...
            }
        - pattern: |
            contract $C {
              ...
              function $CALLER(...) $Y {
                ...
                $FUNC($PARAM2, $PARAM1);
                ...
              }
              ...
              function $FUNC(uint256 $PARAM1, uint256 $PARAM2) $X {
                ...
              }
              ...
            }
        - pattern: |
            contract $C {
              ...
              function $FUNC(..., uint256 $PARAM1, uint256 $PARAM2) $X {
                ...
              }
              ...
              function $CALLER(...) $Y {
                ...
                $FUNC(..., $PARAM2, $PARAM1);
                ...
              }
              ...
            }
        - pattern: |
            contract $C {
              ...
              function $CALLER(...) $Y {
                ...
                $FUNC(..., $PARAM2, $PARAM1);
                ...
              }
              ...
              function $FUNC(..., uint256 $PARAM1, uint256 $PARAM2) $X {
                ...
              }
              ...
            }
        - pattern: |
            contract $C {
              ...
              function $FUNC(..., address $PARAM1, address $PARAM2) $X {
                ...
              }
              ...
              function $CALLER(...) $Y {
                ...
                $FUNC(..., $PARAM2, $PARAM1);
                ...
              }
              ...
            }
        - pattern: |
            contract $C {
              ...
              function $CALLER(...) $Y {
                ...
                $FUNC(..., $PARAM2, $PARAM1);
                ...
              }
              ...
              function $FUNC(..., address $PARAM1, address $PARAM2) $X {
                ...
              }
              ...
            }
        - pattern: |
            contract $C {
              ...
              function $FUNC(..., bytes memory $PARAM1, bytes memory $PARAM2) $X {
                ...
              }
              ...
              function $CALLER(...) $Y {
                ...
                $FUNC(..., $PARAM2, $PARAM1);
                ...
              }
              ...
            }
        - pattern: |
            contract $C {
              ...
              function $CALLER(...) $Y {
                ...
                $FUNC(..., $PARAM2, $PARAM1);
                ...
              }
              ...
              function $FUNC(..., bytes memory $PARAM1, bytes memory $PARAM2) $X {
                ...
              }
              ...
            }
        - pattern: |
            contract $C {
              ...
              function $FUNC($PARAM1, ..., uint256 $PARAM2, uint256 $PARAM3, ..., $PARAM4) $X {
                ...
              }
              ...
              function $CALLER(...) $Y {
                ...
                $FUNC($PARAM1, ..., $PARAM3, $PARAM2, ..., $PARAM4);
                ...
              }
              ...
            }
        - pattern: |
            contract $C {
              ...
              function $CALLER(...) $Y {
                ...
                $FUNC($PARAM1, ..., $PARAM3, $PARAM2, ..., $PARAM4);
                ...
              }
              ...
              function $FUNC($PARAM1, ..., uint256 $PARAM2, uint256 $PARAM3, ..., $PARAM4) $X {
                ...
              }
              ...
            }
        - pattern: |
            contract $C {
              ...
              function $FUNC($PARAM1, ..., address $PARAM2, address $PARAM3, ..., $PARAM4) $X {
                ...
              }
              ...
              function $CALLER(...) $Y {
                ...
                $FUNC($PARAM1, ..., $PARAM3, $PARAM2, ..., $PARAM4);
                ...
              }
              ...
            }
        - pattern: |
            contract $C {
              ...
              function $CALLER(...) $Y {
                ...
                $FUNC($PARAM1, ..., $PARAM3, $PARAM2, ..., $PARAM4);
                ...
              }
              ...
              function $FUNC($PARAM1, ..., address $PARAM2, address $PARAM3, ..., $PARAM4) $X {
                ...
              }
              ...
            }
        - pattern: |
            contract $C {
              ...
              function $FUNC($PARAM1, ..., bytes memory $PARAM2, bytes memory $PARAM3, ..., $PARAM4) $X {
                ...
              }
              ...
              function $CALLER(...) $Y {
                ...
                $FUNC($PARAM1, ..., $PARAM3, $PARAM2, ..., $PARAM4);
                ...
              }
              ...
            }
        - pattern: |
            contract $C {
              ...
              function $CALLER(...) $Y {
                ...
                $FUNC($PARAM1, ..., $PARAM3, $PARAM2, ..., $PARAM4);
                ...
              }
              ...
              function $FUNC($PARAM1, ..., bytes memory $PARAM2, bytes memory $PARAM3, ..., $PARAM4) $X {
                ...
              }
              ...
            }
    languages:
      - solidity
    severity: WARNING