rules:
  - id: missing-address-validation
    patterns:
      - pattern-either:
          # Only flag critical operations that store addresses permanently
          - pattern: |
              function $FUNC(address $ADDR, ...) $VISIBILITY {
                ...
                $OWNER = $ADDR;
                ...
              }
          - pattern: |
              function $FUNC(address $ADDR, ...) $VISIBILITY {
                ...
                $ADMIN = $ADDR;
                ...
              }
          - pattern: |
              function $FUNC(address $ADDR, ...) $VISIBILITY {
                ...
                $TREASURY = $ADDR;
                ...
              }
          # Flag token transfers to potentially zero addresses
          - pattern: |
              function $FUNC(address $TO, ...) $VISIBILITY {
                ...
                $TOKEN.transfer($TO, $AMOUNT);
                ...
              }
          - pattern: |
              function $FUNC(address $TO, ...) $VISIBILITY {
                ...
                payable($TO).transfer($AMOUNT);
                ...
              }
      # Exclude if validation is present
      - pattern-not: |
          function $FUNC(address $ADDR, ...) $VISIBILITY {
            require($ADDR != address(0), ...);
            ...
          }
      - pattern-not: |
          function $FUNC(address $ADDR, ...) $VISIBILITY {
            if ($ADDR == address(0)) revert(...);
            ...
          }
      # Exclude view functions and internal operations
      - pattern-not: |
          function $FUNC(...) $VISIBILITY view {
            ...
          }
      - pattern-not: |
          function $FUNC(...) $VISIBILITY pure {
            ...
          }
      # Exclude functions that might legitimately accept zero address
      - pattern-not: |
          function $FUNC(address $ADDR, ...) $VISIBILITY {
            ...
            emit $EVENT(..., $ADDR, ...);
            ...
          }
    message: "Function '$FUNC' does not validate address parameter against zero address. This can lead to locked funds or loss of control."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Address parameters should be validated to prevent accidental use of zero address in critical operations."

  - id: missing-array-length-validation
    patterns:
      - pattern-either:
          - pattern: |
              function $FUNC(uint256[] memory $ARRAY, ...) $VISIBILITY {
                for (uint256 $I = 0; $I < $ARRAY.length; $I++) {
                  ...
                }
              }
          - pattern: |
              function $FUNC(address[] memory $ADDRESSES, ...) $VISIBILITY {
                for (uint256 $I = 0; $I < $ADDRESSES.length; $I++) {
                  ...
                }
              }
      - pattern-not: |
          function $FUNC(...) $VISIBILITY {
            require($ARRAY.length > 0, ...);
            ...
          }
      - pattern-not: |
          function $FUNC(...) $VISIBILITY {
            require($ARRAY.length <= $MAX_LENGTH, ...);
            ...
          }
    message: "Function '$FUNC' does not validate array length. Empty arrays or excessively large arrays can cause issues."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Array parameters should be validated for minimum and maximum lengths."

  - id: missing-percentage-bounds-check
    patterns:
      - pattern-either:
          - pattern: |
              function $FUNC(..., uint256 $PERCENTAGE, ...) $VISIBILITY {
                ...
                uint256 $RESULT = $AMOUNT * $PERCENTAGE / 100;
                ...
              }
          - pattern: |
              function $FUNC(..., uint256 $BASIS_POINTS, ...) $VISIBILITY {
                ...
                uint256 $RESULT = $AMOUNT * $BASIS_POINTS / 10000;
                ...
              }
      - pattern-not: |
          function $FUNC(..., uint256 $PERCENTAGE, ...) $VISIBILITY {
            require($PERCENTAGE <= 100, ...);
            ...
          }
      - pattern-not: |
          function $FUNC(..., uint256 $BASIS_POINTS, ...) $VISIBILITY {
            require($BASIS_POINTS <= 10000, ...);
            ...
          }
    message: "Function '$FUNC' does not validate percentage/basis points bounds. Values over 100%/10000 can cause unexpected behavior."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Percentage and basis point parameters should be validated against maximum values."

  - id: missing-token-address-validation
    patterns:
      - pattern-either:
          - pattern: |
              function $FUNC(address $TOKEN, ...) $VISIBILITY {
                ...
                IERC20($TOKEN).transfer(...);
                ...
              }
          - pattern: |
              function $FUNC(address $TOKEN, ...) $VISIBILITY {
                ...
                IERC20($TOKEN).transferFrom(...);
                ...
              }
      - pattern-not: |
          function $FUNC(address $TOKEN, ...) $VISIBILITY {
            require($TOKEN != address(0), ...);
            ...
          }
      - pattern-not: |
          function $FUNC(address $TOKEN, ...) $VISIBILITY {
            require($SUPPORTED_TOKENS[$TOKEN], ...);
            ...
          }
    message: "Function '$FUNC' does not validate token address. Invalid tokens can cause transaction failures."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Token addresses should be validated before use in transfers or other operations."

  - id: missing-deadline-validation
    patterns:
      - pattern-either:
          # Only match functions with explicitly named deadline parameters
          - pattern: |
              function $FUNC(..., uint256 deadline) $VISIBILITY {
                ...
              }
          - pattern: |
              function $FUNC(..., uint256 expiry) $VISIBILITY {
                ...
              }
          - pattern: |
              function $FUNC(..., uint256 expiryTime) $VISIBILITY {
                ...
              }
          - pattern: |
              function $FUNC(..., uint256 validUntil) $VISIBILITY {
                ...
              }
          # Match functions that clearly use deadline in trading contexts
          - pattern: |
              function swap(..., uint256 $DEADLINE) $VISIBILITY {
                ...
              }
          - pattern: |
              function addLiquidity(..., uint256 $DEADLINE) $VISIBILITY {
                ...
              }
          - pattern: |
              function removeLiquidity(..., uint256 $DEADLINE) $VISIBILITY {
                ...
              }
      - pattern-not: |
          function $FUNC(..., uint256 deadline) $VISIBILITY {
            require(block.timestamp <= deadline, ...);
            ...
          }
      - pattern-not: |
          function $FUNC(..., uint256 expiry) $VISIBILITY {
            require(block.timestamp <= expiry, ...);
            ...
          }
      - pattern-not: |
          function $FUNC(..., uint256 $DEADLINE) $VISIBILITY {
            require(block.timestamp <= $DEADLINE, ...);
            ...
          }
    message: "Function '$FUNC' accepts deadline/expiry parameter but doesn't validate it. Expired transactions should be rejected."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Deadline parameters should be validated to ensure transactions are executed within the expected timeframe."

  - id: missing-signature-validation
    patterns:
      - pattern-either:
          - pattern: |
              function $FUNC(..., bytes memory $SIGNATURE) $VISIBILITY {
                ...
                address $SIGNER = ecrecover($HASH, $V, $R, $S);
                ...
              }
          - pattern: |
              function $FUNC(..., uint8 $V, bytes32 $R, bytes32 $S) $VISIBILITY {
                ...
                address $SIGNER = ecrecover($HASH, $V, $R, $S);
                ...
              }
      - pattern-not: |
          function $FUNC(...) $VISIBILITY {
            ...
            require($SIGNER != address(0), ...);
            ...
          }
      - pattern-not: |
          function $FUNC(...) $VISIBILITY {
            ...
            require($AUTHORIZED[$SIGNER], ...);
            ...
          }
    message: "Function '$FUNC' uses ecrecover without validating the recovered address. Invalid signatures return zero address."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "ecrecover can return zero address for invalid signatures, which should be validated."

  - id: missing-contract-size-check
    patterns:
      - pattern-either:
          - pattern: |
              function $FUNC(address $CONTRACT, ...) $VISIBILITY {
                ...
                $INTERFACE($CONTRACT).$METHOD(...);
                ...
              }
          - pattern: |
              function $FUNC(address $TARGET, bytes memory $DATA) $VISIBILITY {
                ...
                $TARGET.call($DATA);
                ...
              }
      - pattern-not: |
          function $FUNC(address $CONTRACT, ...) $VISIBILITY {
            require($CONTRACT.code.length > 0, ...);
            ...
          }
    message: "Function '$FUNC' calls external address without verifying it's a contract. Calls to EOAs will succeed silently."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "External calls should verify that the target address contains contract code."

  - id: missing-nonce-validation
    patterns:
      - pattern-either:
          - pattern: |
              function $FUNC(..., uint256 $NONCE, ...) $VISIBILITY {
                ...
                $USED_NONCES[$NONCE] = true;
                ...
              }
          - pattern: |
              function $FUNC(..., uint256 $NONCE, ...) $VISIBILITY {
                ...
                $USER_NONCES[$USER] = $NONCE;
                ...
              }
      - pattern-not: |
          function $FUNC(..., uint256 $NONCE, ...) $VISIBILITY {
            require(!$USED_NONCES[$NONCE], ...);
            ...
          }
      - pattern-not: |
          function $FUNC(..., uint256 $NONCE, ...) $VISIBILITY {
            require($USER_NONCES[$USER] == $NONCE, ...);
            ...
          }
    message: "Function '$FUNC' uses nonce without proper validation. This can lead to replay attacks."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Nonces should be validated to prevent replay attacks and ensure proper ordering."

  - id: missing-price-bounds-validation
    patterns:
      - pattern-either:
          - pattern: |
              function $FUNC(..., uint256 $PRICE, ...) $VISIBILITY {
                ...
                uint256 $AMOUNT = $QUANTITY * $PRICE;
                ...
              }
          - pattern: |
              function setPrice(uint256 $PRICE) $VISIBILITY {
                ...
                $CURRENT_PRICE = $PRICE;
                ...
              }
      - pattern-not: |
          function $FUNC(..., uint256 $PRICE, ...) $VISIBILITY {
            require($PRICE >= $MIN_PRICE && $PRICE <= $MAX_PRICE, ...);
            ...
          }
    message: "Function '$FUNC' does not validate price bounds. Extreme prices can cause overflow or economic attacks."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Price parameters should be validated against reasonable minimum and maximum bounds."

  - id: missing-slippage-bounds-validation
    patterns:
      - pattern-either:
          # Match functions that explicitly calculate slippage
          - pattern: |
              function $FUNC(..., uint256 $SLIPPAGE, ...) $VISIBILITY {
                ...
                uint256 $MIN_OUT = $EXPECTED * (10000 - $SLIPPAGE) / 10000;
                ...
              }
          # Match DEX-style functions with clear slippage parameters
          - pattern: |
              function swapExactTokensForTokens(..., uint256 $MIN_AMOUNT_OUT, ...) $VISIBILITY {
                ...
              }
          - pattern: |
              function swapTokensForExactTokens(..., uint256 $MAX_AMOUNT_IN, ...) $VISIBILITY {
                ...
              }
          - pattern: |
              function addLiquidity(..., uint256 $MIN_A, uint256 $MIN_B, ...) $VISIBILITY {
                ...
              }
          - pattern: |
              function removeLiquidity(..., uint256 $MIN_A, uint256 $MIN_B, ...) $VISIBILITY {
                ...
              }
      - pattern-not: |
          function $FUNC(..., uint256 $SLIPPAGE, ...) $VISIBILITY {
            require($SLIPPAGE <= $MAX_SLIPPAGE, ...);
            ...
          }
      - pattern-not: |
          function $FUNC(..., uint256 $MIN_AMOUNT_OUT, ...) $VISIBILITY {
            require($AMOUNT_OUT >= $MIN_AMOUNT_OUT, ...);
            ...
          }
    message: "Function '$FUNC' performs swaps/trades but does not validate slippage parameter bounds. Excessive slippage can lead to significant losses."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Slippage parameters in trading functions should be validated against maximum acceptable values."
