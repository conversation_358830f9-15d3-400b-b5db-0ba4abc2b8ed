rules:
  - id: integer-overflow-multiplication
    patterns:
      - pattern-either:
          - pattern: |
              uint256 $RESULT = $A * $B;
          - pattern: |
              uint256 $RESULT = $A * $B * $C;
          - pattern: |
              $RESULT = $A * $B;
      - pattern-not: |
          uint256 $RESULT = $A * $B / $DIVISOR;
      - pattern-not-inside: |
          require($A <= type(uint256).max / $B, ...);
      - pattern-not-inside: |
          if ($A > type(uint256).max / $B) revert(...);
    message: "Potential integer overflow in multiplication. Consider using SafeMath or checking for overflow."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Large multiplications can overflow and wrap around, leading to unexpected behavior."

  - id: integer-underflow-subtraction
    patterns:
      - pattern-either:
          - pattern: |
              uint256 $RESULT = $A - $B;
          - pattern: |
              $BALANCE = $BALANCE - $AMOUNT;
      - pattern-not: |
          require($A >= $B, ...);
      - pattern-not: |
          if ($A < $B) revert(...);
      - pattern-not-inside: |
          require($BALANCE >= $AMOUNT, ...);
    message: "Potential integer underflow in subtraction. Ensure the first operand is greater than or equal to the second."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Subtracting a larger number from a smaller one causes underflow in unsigned integers."

  - id: division-by-zero
    patterns:
      - pattern-either:
          - pattern: |
              uint256 $RESULT = $A / $B;
          - pattern: |
              uint256 $RESULT = $A % $B;
      - pattern-not: |
          require($B != 0, ...);
      - pattern-not: |
          require($B > 0, ...);
      - pattern-not: |
          if ($B == 0) revert(...);
    message: "Division by zero vulnerability. Always check that the divisor is not zero."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Division by zero causes transaction reversion and can be used for denial of service attacks."

  - id: precision-loss-division-before-multiplication
    patterns:
      - pattern: |
          uint256 $RESULT = $A / $B * $C;
    message: "Precision loss: division before multiplication. Consider reordering as ($A * $C) / $B."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Division before multiplication can cause significant precision loss in calculations."

  - id: unsafe-casting-larger-to-smaller
    patterns:
      - pattern-either:
          - pattern: |
              uint128 $RESULT = uint128($VALUE);
          - pattern: |
              uint64 $RESULT = uint64($VALUE);
          - pattern: |
              uint32 $RESULT = uint32($VALUE);
          - pattern: |
              uint16 $RESULT = uint16($VALUE);
          - pattern: |
              uint8 $RESULT = uint8($VALUE);
      # Exclude legitimate bit manipulation operations
      - pattern-not: |
          uint32 $RESULT = uint32($VALUE >> $OFFSET);
      - pattern-not: |
          uint16 $RESULT = uint16($VALUE >> $OFFSET);
      - pattern-not: |
          uint8 $RESULT = uint8($VALUE >> $OFFSET);
      # Exclude cases with explicit bounds checking
      - pattern-not: |
          require($VALUE <= type(uint128).max, ...);
      - pattern-not: |
          require($VALUE <= type(uint64).max, ...);
      - pattern-not: |
          require($VALUE <= type(uint32).max, ...);
      - pattern-not: |
          require($VALUE <= type(uint16).max, ...);
      - pattern-not: |
          require($VALUE <= type(uint8).max, ...);
      # Exclude casting of constants or known safe values
      - pattern-not: |
          uint32 $RESULT = uint32($CONSTANT);
      - pattern-not: |
          uint16 $RESULT = uint16($CONSTANT);
      - pattern-not: |
          uint8 $RESULT = uint8($CONSTANT);
      # Exclude casting in bit manipulation contexts (common patterns)
      - pattern-not-inside: |
          $RESULT = ($VALUE << $SHIFT) | uint160($ADDR);
      - pattern-not-inside: |
          $RESULT = $VALUE & $MASK;
    message: "Unsafe casting from larger to smaller integer type without bounds checking. This can cause data truncation."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Casting to smaller integer types can truncate data if the value exceeds the target type's maximum."

  - id: signed-integer-overflow
    patterns:
      - pattern-either:
          - pattern: |
              int256 $RESULT = $A + $B;
          - pattern: |
              int256 $RESULT = $A * $B;
      - pattern-not-inside: |
          require($A <= type(int256).max - $B, ...);
      - pattern-not-inside: |
          require($A <= type(int256).max / $B, ...);
    message: "Potential signed integer overflow. Signed integers can overflow and cause unexpected behavior."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Signed integer overflow can cause unexpected behavior and should be checked."

  - id: unchecked-arithmetic-block
    patterns:
      - pattern: |
          unchecked {
            ...
            $RESULT = $A + $B;
            ...
          }
      - pattern-not: |
          unchecked {
            ...
            require($A <= type(uint256).max - $B, ...);
            $RESULT = $A + $B;
            ...
          }
    message: "Unchecked arithmetic block without manual overflow checks. Ensure operations cannot overflow."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Unchecked blocks disable automatic overflow checks and require manual validation."

  - id: percentage-calculation-overflow
    patterns:
      - pattern-either:
          - pattern: |
              uint256 $RESULT = $AMOUNT * $PERCENTAGE / 100;
          - pattern: |
              uint256 $RESULT = $AMOUNT * $BASIS_POINTS / 10000;
      - pattern-not: |
          require($AMOUNT <= type(uint256).max / $PERCENTAGE, ...);
      - pattern-not: |
          require($AMOUNT <= type(uint256).max / $BASIS_POINTS, ...);
    message: "Percentage calculation may overflow when amount and percentage are large. Check for overflow."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Percentage calculations can overflow when dealing with large amounts and percentages."

  - id: compound-interest-calculation-overflow
    patterns:
      - pattern-either:
          - pattern: |
              uint256 $RESULT = $PRINCIPAL * $RATE ** $TIME;
          - pattern: |
              uint256 $RESULT = $AMOUNT * ($RATE / $BASE) ** $PERIODS;
    message: "Compound interest calculation using exponentiation can easily overflow. Use safer calculation methods."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Exponentiation in compound interest calculations can quickly overflow and should use iterative or library methods."

  - id: token-amount-calculation-precision-loss
    patterns:
      - pattern-either:
          - pattern: |
              uint256 $SHARES = $AMOUNT / $PRICE;
          - pattern: |
              uint256 $AMOUNT = $SHARES / $TOTAL_SHARES * $TOTAL_ASSETS;
    message: "Token amount calculation may suffer from precision loss. Consider using higher precision arithmetic."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Token calculations should maintain precision to avoid rounding errors that can be exploited."

  - id: reward-calculation-rounding-error
    patterns:
      - pattern: |
          uint256 $REWARD = $USER_STAKE * $REWARD_RATE / $TOTAL_STAKE;
      - pattern-not: |
          uint256 $REWARD = $USER_STAKE * $REWARD_RATE * $PRECISION / $TOTAL_STAKE / $PRECISION;
    message: "Reward calculation may have rounding errors. Consider using higher precision or accumulator patterns."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Reward calculations should account for rounding errors to ensure fair distribution."

  - id: price-calculation-manipulation
    patterns:
      - pattern-either:
          - pattern: |
              uint256 $PRICE = $RESERVE_A * 1e18 / $RESERVE_B;
          - pattern: |
              uint256 $PRICE = $BALANCE_A / $BALANCE_B;
      - pattern-not-inside: |
          require($RESERVE_B > $MIN_LIQUIDITY, ...);
    message: "Price calculation based on reserves without minimum liquidity check. This can be manipulated with small amounts."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Price calculations should ensure sufficient liquidity to prevent manipulation with small amounts."

  - id: fee-calculation-precision-loss
    patterns:
      - pattern-either:
          - pattern: |
              uint256 $FEE = $AMOUNT * $FEE_RATE / 10000;
          - pattern: |
              uint256 $FEE = $AMOUNT / $FEE_DIVISOR;
    message: "Fee calculation may lose precision for small amounts. Consider minimum fee or higher precision."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Fee calculations should handle small amounts properly to avoid precision loss."
