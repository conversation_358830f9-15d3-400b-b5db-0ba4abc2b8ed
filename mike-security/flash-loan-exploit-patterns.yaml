rules:
  - id: flash-loan-callback-reentrancy
    patterns:
      - pattern-either:
          - pattern: |
              function $CALLBACK(...) external {
                ...
                $CONTRACT.$FUNC(...);
                ...
              }
          - pattern: |
              function $CALLBACK(...) public {
                ...
                $CONTRACT.$FUNC(...);
                ...
              }
      - metavariable-regex:
          metavariable: $CALLBACK
          regex: .*(flash|Flash).*[Cc]allback.*
      - pattern-not-inside: |
          modifier $GUARD {
            ...
          }
    message: "Flash loan callback function '$CALLBACK' makes external calls without reentrancy protection. Consider implementing reentrancy guards."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Flash loan callbacks that make external calls without reentrancy protection can be exploited for reentrancy attacks."

  - id: flash-loan-price-manipulation
    patterns:
      - pattern-inside: |
          function $CALLBACK(...) external {
            ...
          }
      - pattern-either:
          - pattern: |
              $ROUTER.swap$METHOD(...);
              ...
              $ORACLE.getPrice(...);
          - pattern: |
              $PAIR.swap(...);
              ...
              $ORACLE.getPrice(...);
          - pattern: |
              $ROUTER.swap$METHOD(...);
              ...
              $PAIR.getReserves();
      - metavariable-regex:
          metavariable: $CALLBACK
          regex: .*(flash|Flash).*[Cc]allback.*
    message: "Flash loan callback performs swaps followed by price queries, indicating potential price manipulation attack."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Flash loan callbacks that perform swaps and then query prices can be used for price manipulation attacks."

  - id: flash-loan-without-fee-validation
    patterns:
      - pattern-inside: |
          function $CALLBACK(..., uint256 $FEE, ...) external {
            ...
          }
      - pattern: |
          $TOKEN.transfer($LENDER, $AMOUNT);
      - pattern-not: |
          $TOKEN.transfer($LENDER, $AMOUNT + $FEE);
      - metavariable-regex:
          metavariable: $CALLBACK
          regex: .*(flash|Flash).*[Cc]allback.*
    message: "Flash loan callback repays principal without including fees. This will cause the transaction to fail."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Flash loan callbacks must repay the principal amount plus fees to avoid transaction failure."

  - id: flash-loan-large-amount-pattern
    patterns:
      - pattern-either:
          - pattern: |
              $FLASHLOAN.flashLoan(..., $AMOUNT, ...);
          - pattern: |
              $FLASHLOAN.flash(..., $AMOUNT, ...);
      - metavariable-comparison:
          metavariable: $AMOUNT
          comparison: $AMOUNT > 1000000000000000000000000
    message: "Flash loan requesting extremely large amount ($AMOUNT). Verify this is intentional and not an exploit attempt."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Large flash loan amounts may indicate potential exploit attempts or price manipulation."

  - id: flash-loan-callback-state-changes
    patterns:
      - pattern-inside: |
          function $CALLBACK(...) external {
            ...
          }
      - pattern-either:
          - pattern: |
              $STATE = $VALUE;
          - pattern: |
              $MAPPING[$KEY] = $VALUE;
          - pattern: |
              $ARRAY.push($VALUE);
      - metavariable-regex:
          metavariable: $CALLBACK
          regex: .*(flash|Flash).*[Cc]allback.*
    message: "Flash loan callback modifies contract state. Ensure proper access controls and reentrancy protection."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "State changes in flash loan callbacks should be carefully controlled to prevent exploitation."

  - id: flash-loan-multiple-swaps
    patterns:
      - pattern-inside: |
          function $CALLBACK(...) external {
            ...
          }
      - pattern: |
          $ROUTER1.swap$METHOD1(...);
          ...
          $ROUTER2.swap$METHOD2(...);
      - metavariable-regex:
          metavariable: $CALLBACK
          regex: .*(flash|Flash).*[Cc]allback.*
    message: "Flash loan callback performs multiple swaps, which may indicate arbitrage or price manipulation."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Multiple swaps in flash loan callbacks are common in arbitrage but can also be used for price manipulation."

  - id: flash-loan-liquidity-manipulation
    patterns:
      - pattern-inside: |
          function $CALLBACK(...) external {
            ...
          }
      - pattern-either:
          - pattern: |
              $ROUTER.addLiquidity(...);
              ...
              $ROUTER.removeLiquidity(...);
          - pattern: |
              $PAIR.mint(...);
              ...
              $PAIR.burn(...);
      - metavariable-regex:
          metavariable: $CALLBACK
          regex: .*(flash|Flash).*[Cc]allback.*
    message: "Flash loan callback manipulates liquidity (add then remove), which may be used for price manipulation."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Adding and removing liquidity in flash loan callbacks can be used to manipulate prices and exploit protocols."

  - id: flash-loan-callback-missing-sender-check
    patterns:
      - pattern: |
          function $CALLBACK(...) external {
            ...
          }
      - pattern-not-inside: |
          function $CALLBACK(...) external {
            require(msg.sender == $EXPECTED_SENDER, ...);
            ...
          }
      - pattern-not-inside: |
          function $CALLBACK(...) external {
            if (msg.sender != $EXPECTED_SENDER) revert(...);
            ...
          }
      - metavariable-regex:
          metavariable: $CALLBACK
          regex: .*(flash|Flash).*[Cc]allback.*
    message: "Flash loan callback function '$CALLBACK' does not validate msg.sender. This allows anyone to call the callback."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Flash loan callbacks should validate that they are called by the expected flash loan provider."
