rules:
  - id: auction-price-manipulation-vulnerability
    patterns:
      - pattern-either:
          - pattern: |
              function $FUNC(...) $MODIFIERS {
                ...
                require(msg.value >= $MIN_PRICE, ...);
                ...
                $PRICE_VAR = msg.value;
                ...
              }
          - pattern: |
              function $FUNC(...) $MODIFIERS {
                ...
                if (msg.value > $CURRENT_PRICE) {
                  $PRICE_VAR = msg.value;
                  ...
                }
                ...
              }
          - pattern: |
              function $FUNC(...) $MODIFIERS {
                ...
                $PRICE_VAR = $PRICE_VAR + msg.value;
                ...
              }
    message: "Potential price manipulation vulnerability in function '$FUNC'. Using msg.value directly to set or update prices can lead to inflated prices and potential exploits."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Directly using msg.value to set or update auction prices without proper validation can allow users to artificially inflate prices or manipulate the auction mechanism. Consider using a fixed price or implementing additional checks."