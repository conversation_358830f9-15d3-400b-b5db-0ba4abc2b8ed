rules:
  - id: potential-reentrancy-vulnerability
    patterns:
      - pattern: |
          function $FUNC(...) $MODIFIERS {
            ...
            $ADDRESS.call{value: $AMOUNT}("");
            ...
            $STATE = $NEW_VALUE;
            ...
          }
      - pattern-not: |
          function $FUNC(...) $MODIFIERS {
            ...
            $STATE = $NEW_VALUE;
            ...
            $ADDRESS.call{value: $AMOUNT}("");
            ...
          }
    message: "Potential reentrancy vulnerability in '$FUNC'. Consider using checks-effects-interactions pattern."
    severity: ERROR
    languages: [solidity]