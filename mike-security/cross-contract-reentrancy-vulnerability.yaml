rules:
  - id: cross-contract-reentrancy-vulnerability
    patterns:
      - pattern-either:
          - pattern: |
              function $FUNC(..., $EXTERNAL_CONTRACT, ...) $MODIFIERS {
                ...
                $EXTERNAL_CONTRACT.$EXTERNAL_FUNC(...);
                ...
                $STATE_VARIABLE = $NEW_VALUE;
                ...
              }
          - pattern: |
              function $FUNC(..., $EXTERNAL_CONTRACT, ...) $MODIFIERS {
                ...
                $EXTERNAL_CONTRACT.$EXTERNAL_FUNC(...);
                ...
                $STATE_MAPPING[$KEY] = $NEW_VALUE;
                ...
              }
    message: "Potential cross-contract reentrancy vulnerability detected in function '$FUNC'. Consider updating state before making external calls or implement a shared reentrancy guard across contracts."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Cross-contract reentrancy can occur when state changes are made after external calls, even with individual contract reentrancy guards. Ensure state is updated before external calls or implement a shared guard mechanism."