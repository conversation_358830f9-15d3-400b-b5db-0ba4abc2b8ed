rules:
  - id: oracle-price-manipulation-single-source
    patterns:
      - pattern-either:
          - pattern: |
              uint256 $PRICE = $ORACLE.getPrice(...);
              ...
              require($CONDITION, ...);
          - pattern: |
              uint256 $PRICE = $PAIR.getReserves();
              ...
              require($CONDITION, ...);
      - pattern-not: |
          uint256 $PRICE1 = $ORACLE1.getPrice(...);
          uint256 $PRICE2 = $ORACLE2.getPrice(...);
          ...
    message: "Using single price source for critical operations. Consider implementing multiple oracle sources or TWAP."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Single price sources can be manipulated. Use multiple oracles or time-weighted average prices (TWAP)."

  - id: amm-price-manipulation-vulnerability
    patterns:
      - pattern-either:
          - pattern: |
              $PAIR.swap(...);
              ...
              uint256 $PRICE = $PAIR.getReserves();
          - pattern: |
              $ROUTER.swap$METHOD(...);
              ...
              uint256 $PRICE = $PAIR.getReserves();
          - pattern: |
              $PAIR.swap(...);
              ...
              uint256 $PRICE = $ORACLE.getPrice(...);
    message: "Potential price manipulation: swap operation followed by price query in same transaction."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Performing swaps and then querying prices in the same transaction can be used for price manipulation attacks."

  - id: spot-price-dependency
    patterns:
      - pattern-either:
          - pattern: |
              function $FUNC(...) public {
                ...
                uint256 $PRICE = $PAIR.getReserves();
                ...
                require($PRICE > $VALUE, ...);
                ...
              }
          - pattern: |
              function $FUNC(...) external {
                ...
                uint256 $PRICE = $PAIR.getReserves();
                ...
                require($PRICE > $VALUE, ...);
                ...
              }
          - pattern: |
              function $FUNC(...) public {
                ...
                (uint256 $RESERVE0, uint256 $RESERVE1, ) = $PAIR.getReserves();
                uint256 $PRICE = $RESERVE0 * 1e18 / $RESERVE1;
                ...
              }
          - pattern: |
              function $FUNC(...) external {
                ...
                (uint256 $RESERVE0, uint256 $RESERVE1, ) = $PAIR.getReserves();
                uint256 $PRICE = $RESERVE0 * 1e18 / $RESERVE1;
                ...
              }
      - pattern-not-inside: |
          function $FUNC(...) public view {
            ...
          }
      - pattern-not-inside: |
          function $FUNC(...) external view {
            ...
          }
    message: "Function '$FUNC' depends on spot price from AMM reserves, which can be manipulated within a single transaction."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Spot prices from AMM reserves can be easily manipulated and should not be used for critical operations."

  - id: missing-slippage-protection
    patterns:
      - pattern-either:
          - pattern: |
              $ROUTER.swapExactTokensForTokens($AMOUNT_IN, 0, ...);
          - pattern: |
              $ROUTER.swapTokensForExactTokens($AMOUNT_OUT, type(uint256).max, ...);
          - pattern: |
              $ROUTER.swapExactETHForTokens{value: $VALUE}(0, ...);
          - pattern: |
              $ROUTER.swapExactTokensForETH($AMOUNT_IN, 0, ...);
    message: "Swap operation without slippage protection (amountOutMin=0 or amountInMax=max). This can lead to significant value loss."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Swaps without slippage protection can result in unfavorable trades due to MEV attacks or market volatility."

  - id: oracle-freshness-check-missing
    patterns:
      - pattern: |
          (, int256 $PRICE, , uint256 $TIMESTAMP, ) = $ORACLE.latestRoundData();
      - pattern-not: |
          (, int256 $PRICE, , uint256 $TIMESTAMP, ) = $ORACLE.latestRoundData();
          require(block.timestamp - $TIMESTAMP <= $MAX_DELAY, ...);
    message: "Oracle price data retrieved without checking freshness. Stale prices can be exploited."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Oracle prices should be checked for freshness to avoid using stale data that could be exploited."

  - id: price-calculation-precision-loss
    patterns:
      - pattern-either:
          - pattern: |
              uint256 $PRICE = $A * $B / $C;
          - pattern: |
              uint256 $PRICE = $A / $B * $C;
      - pattern-not: |
          uint256 $PRICE = $A * $B * 1e18 / $C;
    message: "Price calculation may suffer from precision loss. Consider using higher precision arithmetic."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Division before multiplication can cause precision loss in price calculations."

  - id: liquidity-pool-balance-manipulation
    patterns:
      - pattern: |
          uint256 $BALANCE = $TOKEN.balanceOf($PAIR);
          ...
          uint256 $PRICE = $BALANCE * $MULTIPLIER / $DIVISOR;
    message: "Price calculation based on token balance of liquidity pool. This can be manipulated by direct transfers."
    severity: ERROR
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Using token balances of liquidity pools for price calculations can be manipulated by direct token transfers."

  - id: sandwich-attack-vulnerability
    patterns:
      - pattern-inside: |
          function $FUNC(...) external {
            ...
          }
      - pattern: |
          $ROUTER.swapExactTokensForTokens(...);
          ...
          $ROUTER.swapExactTokensForTokens(...);
    message: "Multiple swaps in public function may be vulnerable to sandwich attacks."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Multiple swaps in the same transaction can be front-run and back-run by MEV bots for sandwich attacks."

  - id: twap-manipulation-short-period
    patterns:
      - pattern-either:
          - pattern: |
              $ORACLE.consult($TOKEN, $AMOUNT, $PERIOD);
          - pattern: |
              $ORACLE.getTWAP($TOKEN, $PERIOD);
      - metavariable-comparison:
          metavariable: $PERIOD
          comparison: $PERIOD < 3600
    message: "TWAP period of $PERIOD seconds is too short and may be manipulated. Consider using longer periods (>1 hour)."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Short TWAP periods can be manipulated by sustained attacks. Use longer periods for better security."

  - id: price-impact-not-checked
    patterns:
      - pattern-inside: |
          function $FUNC(...) external {
            ...
          }
      - pattern: |
          uint256 $AMOUNT_OUT = $ROUTER.getAmountsOut($AMOUNT_IN, $PATH)[1];
          $ROUTER.swapExactTokensForTokens($AMOUNT_IN, $MIN_OUT, $PATH, ...);
      - pattern-not: |
          uint256 $AMOUNT_OUT = $ROUTER.getAmountsOut($AMOUNT_IN, $PATH)[1];
          require($AMOUNT_OUT >= $MIN_EXPECTED, ...);
          $ROUTER.swapExactTokensForTokens($AMOUNT_IN, $MIN_OUT, $PATH, ...);
    message: "Large swap without checking price impact. This may indicate potential price manipulation."
    severity: WARNING
    languages: [solidity]
    metadata:
      category: security
      technology: solidity
      description: "Large swaps should check price impact to detect potential manipulation attempts."
