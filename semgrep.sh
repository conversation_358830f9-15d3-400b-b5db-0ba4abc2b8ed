#!/bin/bash

# Check if the correct number of arguments is provided
if [ "$#" -ne 1 ]; then
    echo "Usage: $0 <contracts_folder>"
    exit 1
fi

# Assign the arguments to variables
contracts_dir="$1"
rules_dirs=(
    "/Users/<USER>/projects/web3/contests/semgrep-smart-contracts/solidity/security"
)

# Function to get simplified directory name
get_output_dir() {
    case "$1" in
        "mike-security") echo "mike-security" ;;
        "../semgrep-rules/rules/solidity") echo "semgrep-rules" ;;
        "../semgrep-smart-contracts/solidity/security") echo "semgrep-smart-contracts" ;;
        "sandbox") echo "sandbox" ;;
    esac
}

results_dir="/Users/<USER>/projects/web3/contests/immunefi-all-repos/immunefi-code/semgrep-results"

# Ensure results directory exists
mkdir -p "$results_dir"

#####################################
# Do semgrep on contracts

# Export necessary variables for use in the subshell
export contracts_dir
export results_dir
export rules_dirs

# Initialize counter
counter=0
batch_size=20
files=()

echo "START: Initial cleanup..."
# Remove any existing OpenZeppelin directories from previous runs
find "$results_dir" -type d -name "*OpenZeppelin*" -exec rm -rf {} + 2>/dev/null || true
find "$results_dir" -type d -name "*openzeppelin*" -exec rm -rf {} + 2>/dev/null || true

echo "START: Cleanup complete."

# Find all .sol files in the contracts directory, excluding OpenZeppelin folders, and process each one in parallel batches
find "$contracts_dir" -type f -name "*.sol" | grep -v -i "openzeppelin" | while read -r sol_file; do
    files+=("$sol_file")
    ((counter++))

    if (( counter % batch_size == 0 )); then
        # Process the batch
        for file in "${files[@]}"; do
            for rules_dir in "${rules_dirs[@]}"; do
                (
                    output_dirname=$(get_output_dir "$rules_dir")
                    output_file="$results_dir/${output_dirname}/${file#"$contracts_dir/"}.txt"
                    mkdir -p "$(dirname "$output_file")"
                    semgrep --config="$rules_dir" "$file" --output="$output_file"
                    if [[ ! -s "$output_file" ]]; then
                        rm -f "$output_file"
                    fi
                ) &
            done
        done
        wait
        files=()
    fi
done

# Process remaining files if any
if (( ${#files[@]} > 0 )); then
    for file in "${files[@]}"; do
        for rules_dir in "${rules_dirs[@]}"; do
            (
                output_dirname=$(get_output_dir "$rules_dir")
                output_file="$results_dir/${output_dirname}/${file#"$contracts_dir/"}.txt"
                mkdir -p "$(dirname "$output_file")"
                semgrep --config="$rules_dir" "$file" --output="$output_file"
                if [[ ! -s "$output_file" ]]; then
                    rm -f "$output_file"
                fi
            ) &
        done
    done
    wait
fi

#####################################

echo "Parallel processing complete."

# Check each output directory and remove if it contains no .txt files
echo "END: Checking and cleaning up directories without results..."
for rules_dir in "${rules_dirs[@]}"; do
    output_dirname=$(get_output_dir "$rules_dir")
    output_dir="$results_dir/$output_dirname"

    if [ -d "$output_dir" ]; then
        # Count .txt files in the directory
        txt_count=$(find "$output_dir" -name "*.txt" -type f | wc -l)
        if [ "$txt_count" -eq 0 ]; then
            echo "Removing empty results directory: $output_dir"
            rm -rf "$output_dir"
        else
            echo "Keeping directory $output_dir with $txt_count result files"
        fi
    fi
done

echo "END: Cleanup complete."
